<script setup lang="ts">
import { MessageContent, ImageData as MsgImageData } from "@/core/types/chat.ts";
import { processContent } from "@/utils/messageUtils.ts";
import ImageDisplayComponent from "@/components/MsgItem/ImageDisplayComponent.vue";
import { useMarkdown } from "@/components/MsgItem/useMarkdown.ts";

const props = defineProps<{
    content: MessageContent;
}>();

const { md } = useMarkdown();
</script>

<template>
    <div class="message-content-area">
        <template v-if="typeof content === 'string'">
            <div v-html="md.render(content)"></div>
        </template>
        <template v-else-if="Array.isArray(content)">
            <div
                v-for="(part, index) in content"
                :key="index"
                class="content-part"
            >
                <div
                    v-if="typeof part === 'string'"
                    v-html="md.render(part)"
                ></div>
                <ImageDisplayComponent
                    v-else-if="part.type === 'image'"
                    :image-data="part as MsgImageData"
                />
            </div>
        </template>
        <template v-else-if="content && content.type === 'image'">
            <ImageDisplayComponent :image-data="content as MsgImageData" />
        </template>
    </div>
</template>

<style scoped lang="less">
@import "../../assets/function.less";

.content-part {
    margin-bottom: 8px;
}

.content-part:last-child {
    margin-bottom: 0;
}

:deep(div) {
    hr {
        border-top-width: 0;
    }

    pre {
        overflow-x: auto;
        background-color: var(--color-background-mute);
        padding: 20px;
        margin: 10px 0;
        border-radius: 16px;
    }

    .hljs {
        font-size: 14px;

        .scrollbar-style();
    }

    .code-copy {
        font-size: 14px;
    }

    // table 样式
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 10px 0;
        font-size: 14px;

        th,
        td {
            border: none;
            /* 只保留底部边框，实现行分割 */
            border-bottom: 1px solid var(--border-color-secondary);
            padding: 12px 8px;
            text-align: left;
        }

        th {
            font-weight: bold;
            /* 使用颜色区分表头 */
            border-bottom: 1px solid var(--border-color-primary);
        }

        /* 移除最后一行多余的边框 */
        tbody tr:last-child th,
        tbody tr:last-child td {
            border-bottom: none;
        }
    }
}
</style>
