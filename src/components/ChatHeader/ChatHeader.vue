<script setup lang="ts">
import { computed } from "vue";
import { LayoutSidebarRight, Pencil, Download } from "@vicons/tabler";
import { useChatStore } from "@/store/useChatStore.ts";
import { useEnvStore } from "@/store/useEnvStore.ts";
import { installUpdate } from "@/core/utils/updater.ts";
import ModelSwitch from "./ModelSwitch.vue";

const store = useChatStore();
const envStore = useEnvStore();
const isMsgListEmpty = computed(() => store.activeChat.msgList.length === 0);

async function handleNewChatClick() {
    if (isMsgListEmpty.value) {
        return;
    }
    const chat = await store.createChat();
    store.activateChat(chat.id);
}

function toggleCollapse() {
    store.isSidebarCollapsed = !store.isSidebarCollapsed;
}

async function handleUpgradeClick() {
    try {
        await installUpdate();
    } catch (error) {
        console.error("Failed to install update:", error);
        // 这里可以添加用户通知，比如使用 naive-ui 的 notification 或 message
    }
}
</script>

<template>
    <n-flex
        :wrap="false"
        align="center"
        justify="space-between"
        class="chat-header"
    >
        <div class="flex md:hidden title w-full">
            <n-icon
                size="24"
                class="icon"
                @click="toggleCollapse"
                v-if="store.isSidebarCollapsed"
            >
                <layout-sidebar-right />
            </n-icon>
            <span class="grow"></span>
            <model-switch />
            <span class="grow"></span>
            <n-button
                v-if="envStore.updateDownloaded && envStore.isDesktop"
                type="primary"
                size="small"
                @click="handleUpgradeClick"
                :loading="envStore.updateInstalling"
                :disabled="envStore.updateInstalling"
                class="upgrade-btn"
            >
                {{ envStore.updateInstalling ? "升级中..." : "升级" }}
            </n-button>
            <n-icon
                size="24"
                class="icon"
                @click="handleNewChatClick"
                v-if="store.isSidebarCollapsed"
            >
                <pencil />
            </n-icon>
        </div>
        <div class="flex max-md:hidden title w-full">
            <div>
                <n-icon
                    size="24"
                    class="icon"
                    @click="toggleCollapse"
                    v-if="store.isSidebarCollapsed"
                >
                    <layout-sidebar-right />
                </n-icon>
                <n-icon
                    size="24"
                    class="icon"
                    @click="handleNewChatClick"
                    v-if="store.isSidebarCollapsed"
                >
                    <pencil />
                </n-icon>
            </div>

            <model-switch />

            <div class="flex items-center gap-2">
                <n-button
                    v-if="envStore.updateDownloaded && envStore.isDesktop"
                    type="primary"
                    size="small"
                    @click="handleUpgradeClick"
                    :loading="envStore.updateInstalling"
                    :disabled="envStore.updateInstalling"
                    class="upgrade-btn"
                >
                    {{ envStore.updateInstalling ? "升级中..." : "升级" }}
                </n-button>
            </div>
        </div>
    </n-flex>
</template>

<style scoped lang="less">
.chat-header {
    padding: 16px;
    border-bottom: 1px solid var(--divider-color);

    .title {
        font-size: 16px;
        font-weight: 600;
        align-items: center;
        gap: 8px;
        color: var(--text-secondary-color);
    }

    .icon {
        box-sizing: content-box;
        cursor: pointer;
        padding: 6px;
        border-radius: 9px;
        transition: background-color 0.3s;
        color: var(--icon-color);

        @media screen and (min-width: 768px) {
            &:hover {
                background-color: var(--icon-hover-color);
            }
        }
    }

    .upgrade-btn {
        margin-left: 8px;
        margin-right: 8px;
        font-size: 12px;
        height: 28px;
        padding: 0 12px;
        border-radius: 14px;
        font-weight: 500;
    }
}
</style>
