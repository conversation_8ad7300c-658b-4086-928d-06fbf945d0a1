import { useStorage } from "@vueuse/core";
import { computed, ComputedRef, nextTick, ref } from "vue";
import { defineStore } from "pinia";
import type { Msg, Chat, MessageContent } from "@/core/types/chat.ts";
import { MessageStatus } from "@/core/types/chat.ts";
import { useModelStore } from "@/store/useModelStore.ts";
import { chatService } from "@/services/chatService.ts";
import { messageService } from "@/services/messageService.ts";
import { aiService } from "@/services/aiService.ts";
import { summaryService } from "@/services/summaryService.ts";
import { message } from "@/utils/message.ts";
import { processContent, hasThinkingContent, isThinkingCompleted } from "@/utils/messageUtils.ts";

// 回复风格类型定义
export type ReplyStyle = "default" | "concise" | "detailed";

// 回复风格配置
export const REPLY_STYLE_PROMPTS: Record<ReplyStyle, string> = {
    default: "",
    concise: "请用简洁明了的方式回答，避免冗长的解释。",
    detailed: "请提供详细、全面的回答，包含相关的背景信息和具体示例。"
};

export const REPLY_STYLE_LABELS: Record<ReplyStyle, string> = {
    default: "默认",
    concise: "简洁",
    detailed: "详细"
};

export const useChatStore = defineStore("chat", () => {
    const modelStore = useModelStore();
    const maxMsgCount = useStorage<number>("chat/maxMsgCount", 10);
    const enableAiSummary = useStorage<boolean>("chat/enableAiSummary", false);
    const activeChatIdStorage = ref<string>("0");
    const chatList = ref<Chat[]>([]);
    const isSidebarCollapsed = useStorage<boolean>(
        "config/isSidebarCollapsed",
        false
    );
    const isLoadingMessages = ref(false);
    const isLoadingChats = ref(false);
    const isLoading = ref(false); // Global loading state for sending messages

    // 回复风格状态
    const replyStyle = useStorage<ReplyStyle>("chat/replyStyle", "default");

    // --- Chat Management ---

    async function loadAllChats() {
        isLoadingChats.value = true;
        try {
            chatList.value = await chatService.loadAllChats();
            if (
                chatList.value.length === 0 &&
                activeChatIdStorage.value !== "0"
            ) {
                activeChatIdStorage.value = "0";
            }
        } catch (error) {
            console.error("加载聊天列表失败:", error);
        } finally {
            isLoadingChats.value = false;
        }
    }

    async function activateChat(id: string, isNewChat: boolean = false) {
        if (activeChatIdStorage.value === id && !isNewChat) return;

        activeChatIdStorage.value = id;
        const chat = chatList.value.find((c) => c.id === id);
        if (!chat) return;

        if (isNewChat) {
            chat.msgList = [];
            isLoadingMessages.value = false;
        } else {
            isLoadingMessages.value = true;
            try {
                chat.msgList = await messageService.getChatMessages(id);
            } catch (error) {
                console.error(`加载 [${id}] 的消息失败:`, error);
                chat.msgList = []; // Clear on error
            } finally {
                isLoadingMessages.value = false;
            }
        }
    }

    async function createChat() {
        const newChat = await chatService.createChat();
        chatList.value.push(newChat);
        return newChat;
    }

    async function removeChat(id: string) {
        const idx = chatList.value.findIndex((chat) => chat.id === id);
        if (idx === -1) return;

        chatList.value.splice(idx, 1);
        await chatService.removeChat(id);

        if (id === activeChatId.value) {
            const newActiveId =
                chatList.value.length > 0 ? chatList.value[0].id : "0";
            await activateChat(newActiveId, false);
        }
    }

    async function updateChat(id: string, chat: Partial<Chat>) {
        const idx = chatList.value.findIndex((item) => item.id === id);
        if (idx > -1) {
            Object.assign(chatList.value[idx], chat);
            await chatService.updateChat(id, chat);
        }
    }

    // --- Message Management ---

    function addMessage(chatId: string, message: Msg) {
        const chat = chatList.value.find((c) => c.id === chatId);
        if (chat) {
            chat.msgList.push(message);
            chat.messageCount = (chat.messageCount || 0) + 1;
            chat.lastMsgPreview = message.short;
        }
    }

    function updateMessage(chatId: string, message: Msg) {
        console.log(`[ChatStore] updateMessage for chatId: ${chatId}, status: ${message.status}`, JSON.stringify(message));
        const chat = chatList.value.find((c) => c.id === chatId);
        if (chat) {
            const msgIndex = chat.msgList.findIndex((m) => m.id === message.id);
            if (msgIndex > -1) {
                const oldStatus = chat.msgList[msgIndex].status;
                // 使用 Vue 的响应式方式更新数组元素
                chat.msgList.splice(msgIndex, 1, message);
                console.log(`[ChatStore] Message updated - Status changed from ${oldStatus} to ${message.status}`);
                // If it's the last message, update preview
                if (msgIndex === chat.msgList.length - 1) {
                    chat.lastMsgPreview = message.short;
                }
            } else {
                console.warn(`[ChatStore] Message with id ${message.id} not found in chat ${chatId}`);
            }
        } else {
            console.warn(`[ChatStore] Chat with id ${chatId} not found`);
        }
    }

    async function handleSendMessage(content: MessageContent, resend: boolean = false, assistantMessageId?: string) {
        let assistantMsg: Msg | undefined;
        isLoading.value = true;

        try {
            if (!resend && !content) {
                throw new Error("消息不能为空");
            }

            const openai = await modelStore.createOpenAIInstance();
            if (!openai) {
                const errorReason = "无法创建 OpenAI 实例，请检查供应商配置。";
                message.error(errorReason);
                throw new Error(errorReason);
            }

            let currentChatId = activeChatId.value;
            if (currentChatId === "0") {
                const newChat = await createChat();
                await activateChat(newChat.id, true);
                currentChatId = newChat.id;
            }

            const currentModel = modelStore.model;

            if (!resend) {
                const userMessage = await messageService.addMessage(
                    currentChatId,
                    content,
                    "user",
                    { model: currentModel }
                );
                addMessage(currentChatId, userMessage);
            }

            if (resend && assistantMessageId) {
                assistantMsg = await messageService.updateMsg(
                    assistantMessageId,
                    {
                        content: "",
                        status: MessageStatus.WAITING,
                        model: currentModel,
                    }
                );
                updateMessage(currentChatId, assistantMsg);
            } else {
                assistantMsg = await messageService.addMessage(
                    currentChatId,
                    "",
                    "assistant",
                    {
                        short: "AI思考中...",
                        status: MessageStatus.WAITING,
                        model: currentModel,
                    }
                );
                addMessage(currentChatId, assistantMsg);
            }

            const effectiveMaxMsgCount = maxMsgCount.value;
            let fullResponse = "";

            // 获取当前回复风格的系统提示词
            const stylePrompt = REPLY_STYLE_PROMPTS[replyStyle.value];

            await aiService.sendMessageToAI(
                currentChatId,
                openai,
                currentModel,
                resend ? null : content,
                {
                    resend,
                    assistantMessageId: assistantMsg.id,
                    maxMsgCount: effectiveMaxMsgCount,
                    systemPrompt: stylePrompt,
                    onNewChunk: (chunk) => {
                        console.log("[ChatStore] onNewChunk called with chunk:", chunk);
                        fullResponse += chunk;
                        if (assistantMsg) {
                            // 实时处理思考内容分离
                            const processed = processContent(fullResponse);
                            console.log("[ChatStore] Processed content:", {
                                hasThinking: hasThinkingContent(fullResponse),
                                isCompleted: isThinkingCompleted(fullResponse),
                                mainContentLength: processed.mainContent.length,
                                thinkContentLength: processed.thinkContent?.length || 0,
                                fullResponseLength: fullResponse.length,
                                containsEndTag: fullResponse.includes("</think>"),
                                lastChars: fullResponse.slice(-20) // 显示最后20个字符
                            });

                            // 根据思维链状态确定消息状态
                            let messageStatus: MessageStatus;
                            if (hasThinkingContent(fullResponse)) {
                                // 如果包含思维链内容
                                if (isThinkingCompleted(fullResponse)) {
                                    // 思维链已完成，正在生成正文
                                    messageStatus = MessageStatus.GENERATING;
                                    console.log("[ChatStore] Thinking completed, switching to GENERATING");
                                } else {
                                    // 思维链进行中
                                    messageStatus = MessageStatus.THINKING;
                                    console.log("[ChatStore] Thinking in progress, status: THINKING");
                                }
                            } else {
                                // 没有思维链，直接生成正文
                                messageStatus = MessageStatus.GENERATING;
                                console.log("[ChatStore] No thinking content, status: GENERATING");
                            }

                            const updatedMsg = {
                                ...assistantMsg,
                                content: processed.mainContent,
                                thinkContent: processed.thinkContent || undefined,
                                status: messageStatus,
                            };
                            console.log("[ChatStore] onNewChunk, updating message with status:", messageStatus);
                            updateMessage(currentChatId, updatedMsg);
                            assistantMsg = updatedMsg;
                        } else {
                            console.warn("[ChatStore] onNewChunk called but assistantMsg is null");
                        }
                    },
                }
            );

            if (assistantMsg) {
                const finalAssistantMsg = await messageService.updateMsg(assistantMsg.id, {
                    content: assistantMsg.content,
                    thinkContent: assistantMsg.thinkContent || undefined,
                    status: MessageStatus.COMPLETED,
                });
                console.log("[ChatStore] Final message update after stream:", JSON.stringify(finalAssistantMsg));
                updateMessage(currentChatId, finalAssistantMsg);
            }

            const isFirstTurn = (activeChat.value?.messageCount ?? 0) <= 2;
            if (isFirstTurn) {
                // 使用主要内容生成标题，过滤掉思考内容
                const processed = processContent(fullResponse);
                const mainContentForTitle = processed.mainContent || fullResponse;

                let summary = "";
                if (enableAiSummary.value) {
                    try {
                        summary =
                            await summaryService.generateSummary(
                                openai,
                                currentModel,
                                mainContentForTitle
                            );
                    } catch (e) {
                        console.error("生成摘要失败", e);
                        summary = mainContentForTitle.substring(0, 20);
                    }
                } else {
                    summary = mainContentForTitle.substring(0, 20);
                }

                if (summary) {
                    await updateChat(currentChatId, {
                        short: summary,
                    });
                }
            }

        } catch (error) {
            const err =
                error instanceof Error
                    ? error
                    : new Error("An unknown error occurred");
            message.error(err.message);

            if (assistantMsg) {
                const updatedMsg = await messageService.updateMsg(
                    assistantMsg.id,
                    {
                        content: `发生错误: ${err.message}`,
                        status: MessageStatus.ERROR,
                    }
                );
                updateMessage(activeChatId.value, updatedMsg);
            }
        } finally {
            isLoading.value = false;
        }
    }

    // --- Computed Properties & Refs ---

    const activeChatId = computed(() => activeChatIdStorage.value);

    const activeChat: ComputedRef<Chat> = computed(() => {
        return (
            chatList.value.find((item) => item.id === activeChatId.value) || {
                id: "0",
                msgList: [],
                short: "",
                time: 0,
                messageCount: 0,
                lastMsgPreview: "",
            }
        );
    });

    const scrollTo = ref(
        (position = "bottom", onlyScrollWhenAtBottom = false) => {
            console.warn("未初始化滚动函数", position, onlyScrollWhenAtBottom);
        }
    );

    // --- Initial Load ---
    loadAllChats();

    return {
        chatList,
        createChat,
        removeChat,
        updateChat,
        activateChat,
        activeChat,
        activeChatId,
        scrollTo,
        isLoading,
        isSidebarCollapsed,
        maxMsgCount,
        isLoadingMessages,
        isLoadingChats,
        loadAllChats,
        addMessage,
        updateMessage,
        enableAiSummary,
        handleSendMessage,
        // 回复风格相关
        replyStyle,
    };
});
