import { IA<PERSON>rovider, SendMessageParams } from '../AIProvider';
import { aiService } from '@/services/aiService';
import { useModelStore } from '@/store/useModelStore';

export class AI<PERSON>roviderImpl implements IAIProvider {
  async sendMessage(params: SendMessageParams): Promise<AsyncIterable<string>> {
    const { chatId, content, maxMessages = 10 } = params;

    // 获取模型配置
    const modelStore = useModelStore();
    const openai = await modelStore.createOpenAIInstance();
    const model = modelStore.model;

    if (!openai) {
      throw new Error('OpenAI client not initialized');
    }

    if (!model) {
      throw new Error('No model selected');
    }

    // 创建一个异步生成器来处理流式响应
    return this.createStreamGenerator(chatId, openai, model, content, maxMessages);
  }

  private async *createStreamGenerator(
    chatId: string,
    openai: any,
    model: string,
    content: any,
    maxMessages: number
  ): AsyncGenerator<string, void, unknown> {
    let chunks: string[] = [];
    let resolveChunk: ((value: string) => void) | null = null;
    let rejectChunk: ((error: any) => void) | null = null;
    let isComplete = false;
    let error: any = null;

    // 使用现有的 aiService.sendMessageToAI 方法
    const sendPromise = aiService.sendMessageToAI(
      chatId,
      openai,
      model,
      content,
      {
        maxMsgCount: maxMessages,
        onNewChunk: (chunk: string) => {
          chunks.push(chunk);
          if (resolveChunk) {
            resolveChunk(chunk);
            resolveChunk = null;
            rejectChunk = null;
          }
        }
      }
    ).then(() => {
      isComplete = true;
      if (resolveChunk) {
        resolveChunk(''); // 发送空字符串表示完成
        resolveChunk = null;
        rejectChunk = null;
      }
    }).catch((err) => {
      error = err;
      isComplete = true;
      if (rejectChunk) {
        rejectChunk(err);
        resolveChunk = null;
        rejectChunk = null;
      }
    });

    let chunkIndex = 0;
    
    while (!isComplete || chunkIndex < chunks.length) {
      if (error) {
        throw error;
      }
      
      if (chunkIndex < chunks.length) {
        yield chunks[chunkIndex];
        chunkIndex++;
      } else if (!isComplete) {
        // 等待下一个 chunk
        try {
          const chunk = await new Promise<string>((resolve, reject) => {
            resolveChunk = resolve;
            rejectChunk = reject;
          });
          if (chunk) {
            yield chunk;
            chunkIndex++;
          }
        } catch (err) {
          error = err;
          throw err;
        }
      } else {
        break;
      }
    }

    // 等待发送完成
    await sendPromise;
  }
}
