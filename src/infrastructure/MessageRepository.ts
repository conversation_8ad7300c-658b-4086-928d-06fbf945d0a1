
import type { Msg } from '@/core/types/chat';

export interface CreateMessageParams {
  chatId: string;
  content: any; // Define a proper type for content
  role: 'user' | 'assistant';
}

export interface UpdateMessageParams {
  messageId: string;
  content?: any;
  thinkContent?: any;
  status?: any; // Define a proper type for status
}

export interface IMessageRepository {
  create(params: CreateMessageParams): Promise<Msg>;
  update(params: UpdateMessageParams): Promise<void>;
  findById(messageId: string): Promise<Msg | undefined>;
}
