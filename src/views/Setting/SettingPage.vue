<template>
    <div class="w-full h-full flex flex-col">
        <!-- 设置页面头部 -->
        <app-header>
            <div
                class="flex items-center justify-between title w-full pl-4 pr-4"
            >
                <p class="text-xl font-bold dark:text-white">
                    设置
                </p>
                <n-button @click="handleBack">返回</n-button>
            </div>
        </app-header>
        <!-- 固定菜单区域 -->
        <div class="setting-menu">
            <div class="flex space-x-4">
                <div
                    v-for="item in menuOptions"
                    :key="item.key"
                    @click="handleMenuUpdate(item.key)"
                    :class="[
                        'menu-item',
                        activeKey === item.key
                            ? 'menu-item-active'
                            : 'menu-item-normal',
                    ]"
                >
                    <div class="text-lg mb-1">
                        <n-icon>
                            <component :is="item.icon" />
                        </n-icon>
                    </div>
                    <span class="text-xs">{{ item.label }}</span>
                </div>
            </div>
        </div>

        <!-- 可滚动内容区域 -->
        <div class="setting-content">
            <!-- API设置 -->
            <div v-show="activeKey === 'api'" class="content-section !p-0">
                <api-setting-form />
            </div>

            <!-- 对话设置 -->
            <div v-show="activeKey === 'chat-settings'" class="content-section">
                <p class="section-title">对话历史设置</p>
                <div class="flex flex-col gap-2">
                    <span class="setting-label"
                        >发送消息时截取的历史消息条数：</span
                    >
                    <n-input-number
                        v-model:value="chatStore.maxMsgCount"
                        :min="0"
                        :max="100"
                        placeholder="0"
                        size="small"
                        class="w-24"
                    />
                    <span class="setting-hint"
                        >（0表示不限制，建议10-20条）</span
                    >
                </div>
                <p class="setting-description">
                    较低的消息条数可以减少token消耗，提高响应速度，但可能影响AI的上下文理解能力。
                </p>
            </div>

            <!-- 界面设置 -->
            <div v-show="activeKey === 'ui'" class="content-section">
                <p class="section-title">界面设置</p>
                <div class="flex items-center justify-between">
                    <span class="setting-label">AI 总结会话</span>
                    <n-switch v-model:value="chatStore.enableAiSummary" />
                </div>
                <p class="setting-description">
                    开启后，新建的会话将由 AI
                    进行总结生成标题。关闭则直接使用消息内容作为标题。
                </p>
            </div>

            <!-- 数据导出 -->
            <div v-show="activeKey === 'data'" class="content-section">
                <p class="section-title">数据导出</p>
                <p class="setting-description mb-4">
                    导出您的模型配置和会话历史记录为JSON格式文件，便于备份和迁移。
                </p>

                <div class="space-y-4">
                    <!-- 模型配置导出 -->
                    <div class="export-item">
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="setting-label">模型配置</span>
                                <p class="setting-description mt-1">
                                    导出所有供应商配置信息，包括API密钥、BaseURL等设置
                                </p>
                            </div>
                            <n-button @click="exportModelConfig" type="primary" size="small">
                                导出配置
                            </n-button>
                        </div>
                    </div>

                    <!-- 会话历史记录导出 -->
                    <div class="export-item">
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="setting-label">会话历史记录</span>
                                <p class="setting-description mt-1">
                                    导出所有会话的聊天记录，包括消息内容、时间戳等信息
                                </p>
                            </div>
                            <n-button @click="exportChatHistory" type="primary" size="small">
                                导出记录
                            </n-button>
                        </div>
                    </div>

                    <!-- 完整数据导出 -->
                    <div class="export-item">
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="setting-label">完整数据</span>
                                <p class="setting-description mt-1">
                                    导出所有数据，包括模型配置和会话历史记录
                                </p>
                            </div>
                            <n-button @click="exportAllData" type="primary" size="small">
                                导出全部
                            </n-button>
                        </div>
                    </div>
                </div>

                <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <p class="text-sm text-blue-700 dark:text-blue-300">
                        <strong>注意：</strong>导出的文件包含敏感信息（如API密钥），请妥善保管，避免泄露。
                    </p>
                </div>
            </div>

            <!-- 使用指南 -->
            <div v-show="activeKey === 'guide'" class="content-section">
                <p class="section-title">怎么获取BaseURL和密钥？</p>
                <p class="setting-description">
                    BaseURL和密钥可以从大模型服务开发者控制台取得。
                    <br />
                    以DeepSeek为例：<a
                        href="https://platform.deepseek.com/api_keys"
                        target="_blank"
                        class="setting-link"
                        >https://platform.deepseek.com/api_keys</a
                    >
                </p>
            </div>

            <!-- 免责声明 -->
            <div v-show="activeKey === 'disclaimer'" class="content-section">
                <p class="section-title">生成内容由DeepSeek负责</p>
            </div>

            <!-- 问题反馈 -->
            <div v-show="activeKey === 'feedback'" class="content-section">
                <div class="setting-description">
                    微信: iloveos
                    <br />
                    邮箱: <EMAIL>
                </div>
            </div>

            <!-- 关于 -->
            <div v-show="activeKey === 'about'" class="content-section">
                <p class="section-title">Chat Assistant</p>
                <p class="setting-description">版本：1.0.0</p>
                <p class="setting-description">为 LLM 开发的桌面聊天应用</p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { NInputNumber, NSwitch, NButton, useMessage } from "naive-ui";
import { useRouter } from "vue-router";
import ApiSettingForm from "@/views/Setting/components/ApiSettingForm.vue";
import AppHeader from "@/components/AppHeader/AppHeader.vue";
import { useChatStore } from "@/store/useChatStore.ts";
import { useModelStore } from "@/store/useModelStore.ts";
import { chatService } from "@/services/chatService.ts";
import { messageService } from "@/services/messageService.ts";
import {
    SettingOutlined,
    ApiOutlined,
    FormatPainterOutlined,
    QuestionCircleOutlined,
    InfoCircleOutlined,
    MessageOutlined,
    ExclamationCircleOutlined,
    ArrowLeftOutlined,
    DatabaseOutlined,
} from "@vicons/antd";

const router = useRouter();
const message = useMessage();

/* 获取聊天存储 */
const chatStore = useChatStore();
const modelStore = useModelStore();

// 菜单选项
const menuOptions = [
    {
        label: "API 设置",
        key: "api",
        icon: ApiOutlined,
    },
    {
        label: "对话设置",
        key: "chat-settings",
        icon: SettingOutlined,
    },
    {
        label: "界面设置",
        key: "ui",
        icon: FormatPainterOutlined,
    },
    {
        label: "数据",
        key: "data",
        icon: DatabaseOutlined,
    },
    {
        label: "使用指南",
        key: "guide",
        icon: QuestionCircleOutlined,
    },
    {
        label: "免责声明",
        key: "disclaimer",
        icon: ExclamationCircleOutlined,
    },
    {
        label: "问题反馈",
        key: "feedback",
        icon: MessageOutlined,
    },
    {
        label: "关于",
        key: "about",
        icon: InfoCircleOutlined,
    },
];

// 当前选中的菜单项
const activeKey = ref("api");

// 处理菜单选择变更
const handleMenuUpdate = (key: string) => {
    activeKey.value = key;
};

// 处理返回按钮点击
const handleBack = () => {
    router.push("/");
};

// 导出模型配置
const exportModelConfig = async () => {
    try {
        const config = {
            providers: modelStore.providerList,
            exportTime: new Date().toISOString(),
            version: "1.0.0"
        };

        const dataStr = JSON.stringify(config, null, 2);
        const blob = new Blob([dataStr], { type: "application/json" });
        const url = URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = url;
        link.download = `model-config-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        message.success("模型配置导出成功");
    } catch (error) {
        console.error("导出模型配置失败:", error);
        message.error("导出模型配置失败");
    }
};

// 导出会话历史记录
const exportChatHistory = async () => {
    try {
        // 获取所有聊天
        const chats = await chatService.loadAllChats();

        // 为每个聊天获取消息
        const chatsWithMessages = await Promise.all(
            chats.map(async (chat) => {
                const messages = await messageService.getChatMessages(chat.id);
                return {
                    ...chat,
                    messages: messages
                };
            })
        );

        const chatHistory = {
            chats: chatsWithMessages,
            exportTime: new Date().toISOString(),
            version: "1.0.0"
        };

        const dataStr = JSON.stringify(chatHistory, null, 2);
        const blob = new Blob([dataStr], { type: "application/json" });
        const url = URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = url;
        link.download = `chat-history-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        message.success("会话历史记录导出成功");
    } catch (error) {
        console.error("导出会话历史记录失败:", error);
        message.error("导出会话历史记录失败");
    }
};

// 导出所有数据
const exportAllData = async () => {
    try {
        // 获取所有聊天
        const chats = await chatService.loadAllChats();

        // 为每个聊天获取消息
        const chatsWithMessages = await Promise.all(
            chats.map(async (chat) => {
                const messages = await messageService.getChatMessages(chat.id);
                return {
                    ...chat,
                    messages: messages
                };
            })
        );

        const allData = {
            modelConfig: {
                providers: modelStore.providerList,
            },
            chatHistory: {
                chats: chatsWithMessages,
            },
            exportTime: new Date().toISOString(),
            version: "1.0.0"
        };

        const dataStr = JSON.stringify(allData, null, 2);
        const blob = new Blob([dataStr], { type: "application/json" });
        const url = URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = url;
        link.download = `chat-assistant-data-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        message.success("所有数据导出成功");
    } catch (error) {
        console.error("导出所有数据失败:", error);
        message.error("导出所有数据失败");
    }
};
</script>

<style scoped>
/* 设置页面头部 */
.setting-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color-primary);
    background-color: var(--color-background);
    flex-shrink: 0;
}

/* 设置菜单区域 */
.setting-menu {
    border-bottom: 1px solid var(--border-color-primary);
    padding: 0.5rem 1rem;
    background-color: var(--color-background);
    flex-shrink: 0;
}

/* 菜单项 */
.menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    color: var(--color-text);
}

.menu-item-normal:hover {
    background-color: var(--side-hover-bg-color);
}

.menu-item-active {
    background-color: var(--side-active-bg-color);
    color: var(--side-text-color);
}

/* 内容区域 */
.setting-content {
    flex: 1;
    overflow: auto;
    background-color: var(--color-background-soft);
}

.content-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1.5rem;
    height: 100%;
    overflow: auto;
}

/* 标题样式 */
.section-title {
    font-weight: bold;
    color: var(--color-text);
    margin-bottom: 0.5rem;
}

/* 设置标签 */
.setting-label {
    display: block;
    color: var(--color-text);
    font-size: 0.875rem;
}

/* 提示文字 */
.setting-hint {
    font-size: 0.75rem;
    color: var(--text-secondary-color);
}

/* 描述文字 */
.setting-description {
    font-size: 0.75rem;
    color: var(--text-secondary-color);
    margin-top: 0.5rem;
    line-height: 1.5;
}

/* 链接样式 */
.setting-link {
    color: #0066cc;
    text-decoration: none;
}

.setting-link:hover {
    text-decoration: underline;
}

/* 导出项样式 */
.export-item {
    padding: 1rem;
    border: 1px solid var(--border-color-primary);
    border-radius: 8px;
    background-color: var(--color-background);
    transition: all 0.2s ease;
}

.export-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@media (prefers-color-scheme: dark) {
    .setting-link {
        color: #4da6ff;
    }
}
</style>
