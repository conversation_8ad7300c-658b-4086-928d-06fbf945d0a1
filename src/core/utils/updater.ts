import { check } from "@tauri-apps/plugin-updater";
import { relaunch } from "@tauri-apps/plugin-process";
import { useEnvStore } from "../store/useEnvStore";

// 全局变量存储更新实例
let pendingUpdate: any = null;

export async function checkAndDownloadUpdate() {
    const envStore = useEnvStore();

    // 移动端不支持自更新，跳过
    if (envStore.isMobile) {
        console.log("Mobile platform detected, skipping auto-update");
        return;
    }
    const update = await check();
    if (update) {
        console.log(
            `found update ${update.version} from ${update.date} with notes ${update.body}`
        );

        // Update store state
        envStore.updateAvailable = true;
        envStore.updateInfo = {
            version: update.version || "",
            notes: update.body || "",
            date: update.date || "",
        };

        // 存储更新实例以供后续安装使用
        pendingUpdate = update;

        // 静默下载更新
        let downloaded = 0;
        let contentLength = 0;
        await update.download((event) => {
            switch (event.event) {
                case "Started":
                    contentLength = event.data.contentLength || 0;
                    console.log(
                        `started downloading ${event.data.contentLength} bytes`
                    );
                    break;
                case "Progress":
                    downloaded += event.data.chunkLength;
                    console.log(
                        `downloaded ${downloaded} from ${contentLength}`
                    );
                    break;
                case "Finished":
                    console.log("download finished");
                    // 下载完成，设置状态
                    envStore.updateDownloaded = true;
                    break;
            }
        });

        console.log("update downloaded and ready to install");
    }
}

export async function installUpdate() {
    const envStore = useEnvStore();
    if (pendingUpdate) {
        try {
            envStore.updateInstalling = true;
            console.log("installing update...");
            await pendingUpdate.install();
            console.log("update installed, relaunching...");
            await relaunch();
        } catch (error) {
            console.error("Failed to install update:", error);
            envStore.updateInstalling = false;
            throw error;
        }
    } else {
        console.error("No pending update to install");
        throw new Error("No pending update to install");
    }
}

// 向后兼容的函数
export async function checkAndUpdate() {
    await checkAndDownloadUpdate();
}
