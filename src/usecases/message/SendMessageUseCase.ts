import { UseCase } from '@/usecases/base/UseCase';
import { IMessageRepository, CreateMessageParams } from '@/infrastructure/MessageRepository';
import { IAIProvider } from '@/infrastructure/AIProvider';
import { IChatRepository } from '@/infrastructure/ChatRepository';
import { ProcessThinkingUseCase } from './ProcessThinkingUseCase';
import { UpdateMessageUseCase } from './UpdateMessageUseCase';
import { Msg, MessageContent, Chat, MessageStatus } from '@/core/types/chat';
import { v4 as uuid } from 'uuid';

export interface SendMessageInput {
  chatId: string;
  content: MessageContent;
  resend?: boolean;
  assistantMessageId?: string;
}

export interface SendMessageOutput {
  userMessage?: Msg;
  assistantMessage: Msg;
  success: boolean;
}

export class SendMessageUseCase extends UseCase<SendMessageInput, SendMessageOutput> {
  constructor(
    private messageRepo: IMessageRepository,
    private aiProvider: IAIProvider,
    private chatRepo: IChatRepository,
    private processThinking: ProcessThinkingUseCase,
    private updateMessage: UpdateMessageUseCase
  ) {
    super();
  }

  async execute(input: SendMessageInput): Promise<SendMessageOutput> {
    this.validateInput(input);

    const chat = await this.ensureChatExists(input.chatId);

    let userMessage: Msg | undefined;
    if (!input.resend) {
      userMessage = await this.messageRepo.create({
        chatId: chat.id,
        content: input.content,
        role: 'user',
      });
    }

    const assistantMessage = await this.createOrUpdateAssistantMessage(input);

    await this.handleAIResponse(chat.id, assistantMessage.id, input);

    return {
      userMessage,
      assistantMessage,
      success: true,
    };
  }

  private validateInput(input: SendMessageInput) {
    if (!input.chatId || input.chatId === '0') {
      throw new Error('No active session, cannot send message');
    }
  }

  private async ensureChatExists(chatId: string): Promise<Chat> {
    let chat = await this.chatRepo.findById(chatId);
    if (!chat) {
        // This part of the logic for creating a new chat needs to be clarified.
        // For now, we'll assume the chat exists.
        // In a real scenario, you might create a new chat here.
        throw new Error(`Chat with id ${chatId} not found.`);
    }
    return chat;
  }

  private async createOrUpdateAssistantMessage(input: SendMessageInput): Promise<Msg> {
    if (input.assistantMessageId) {
        const existingMsg = await this.messageRepo.findById(input.assistantMessageId);
        if(existingMsg) return existingMsg;
    }
    
    const assistantMessageParams: CreateMessageParams = {
        chatId: input.chatId,
        content: '',
        role: 'assistant',
    };

    return this.messageRepo.create(assistantMessageParams);
  }

  private async handleAIResponse(chatId: string, messageId: string, input: SendMessageInput) {
    const stream = await this.aiProvider.sendMessage({
      chatId,
      content: input.content,
      // maxMessages: 10 // This should be part of the provider's internal logic or a config
    });

    let fullContent = '';
    for await (const chunk of stream) {
      fullContent += chunk;

      // It's better to process thinking and update the message inside the stream loop
      const thinkingResult = await this.processThinking.execute({ content: fullContent, messageId });

      await this.updateMessage.execute({
        messageId,
        content: thinkingResult.mainContent,
        thinkContent: thinkingResult.thinkContent,
        status: thinkingResult.status,
      });
    }
  }
}